# 建议的命令

## Windows系统命令
由于项目运行在Windows系统上，以下是常用的系统命令：

### 文件操作
- `dir` - 列出目录内容
- `cd` - 切换目录
- `copy` - 复制文件
- `move` - 移动文件
- `del` - 删除文件

### 项目运行
- `python main.py` - 运行主应用程序
- `python -m pip install PySide2` - 安装PySide2依赖

### 开发工具
- `python -m py_compile filename.py` - 编译Python文件
- `python -c "import sys; print(sys.version)"` - 检查Python版本

### Git操作
- `git status` - 查看仓库状态
- `git add .` - 添加所有更改
- `git commit -m "message"` - 提交更改
- `git push` - 推送到远程仓库

### 调试和测试
- `python -i script.py` - 交互式运行脚本
- `python -m pdb script.py` - 使用调试器运行

## 项目特定命令
- 确保ZMotion控制器DLL文件在正确路径
- 检查控制器IP地址配置
- 验证串口连接设置