# ZMotion ZAUXDLL Python API 参考文档

本文档包含了ZMotion控制器Python接口库(zauxdllPython.py)中ZAUXDLL类的所有方法。

## 目录

- [连接管理](#连接管理)
- [命令执行](#命令执行)
- [系统控制](#系统控制)
- [IO控制](#io控制)
- [模拟量控制](#模拟量控制)
- [PWM控制](#pwm控制)
- [轴参数设置](#轴参数设置)
- [轴状态读取](#轴状态读取)
- [运动控制](#运动控制)
- [插补运动](#插补运动)
- [回零控制](#回零控制)
- [总线控制](#总线控制)
- [数据读写](#数据读写)

---

## 连接管理

### ZAux_OpenEth

**描述：** 与控制器建立链接。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ipaddress | sting | IP地址,字符串的方式输入 |

**返回值：** 错误码。                            type: int32

---

### ZAux_SearchEthlist

---

### ZAux_SearchEth

---

### ZAux_OpenCom

**描述：** 与控制器建立链接，串口方式。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| comid | uint32 | 串口号 |

**返回值：** 错误码。 type: int32

---

### ZAux_Close

**描述：** 关闭控制器链接。

**返回值：** 错误码。 type: int32

---

### ZAux_SearchAndOpenCom

**描述：** 快速控制器建立链接。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| uimincomidfind | uint32 | 最小串口号 |
| uimincomidfind | uint32 | 最大串口号 |
| uims | uint32 | 链接时间 |

**返回值：** 错误码,有效COM,卡链接handle 。 type: int32,uint

---

### ZAux_OpenEth

**描述：** 与控制器建立链接。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ipaddr | sting | IP地址,字符串的方式输入 |

**返回值：** 错误码。 type: int32

---

### ZAux_OpenPci

**描述：** 与控制器建立链接。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| cardnum | uint32 | PCI卡号 |

**返回值：** 错误码。             type: int32

---

### ZAux_FastOpen

**描述：** 与控制器建立链接, 可以指定连接的等待时间。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| type | int | 连接类型   1-COM 2-ETH 3-预留USB 4-PCI |
| pconnectstring | sting | 连接字符串 pconnectstring  COM口号/IP地址 |
| uims | int | 连接超时时间 uims |

**返回值：** 错误码。    type:int32

---

## 命令执行

### ZAux_Execute

**描述：** 封装 Excute 函数, 以便接收错误。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| pszCommand | sting | 字符串命令 |
| uiResponseLength | uint32 | 返回的字符长度 |

**返回值：** 错误码,返回的字符串。              type: int32,sting

---

### ZAux_DirectCommand

**描述：** 封装 DirectCommand 函数, 以便接收错误。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| pszCommand | sting | 字符串命令 |
| uiResponseLength | uint32 | 返回的字符长度 |

**返回值：** 错误码,返回的字符串。              type: int32,sting

---

## 系统控制

### ZAux_Resume

**描述：** 暂停继续运行BAS项目。

**返回值：** 错误码。 type: int32

---

### ZAux_Pause

**描述：** 暂停控制器中BAS程序

**返回值：** 错误码。 type: int32

---

### ZAux_BasDown

**描述：** 单个BAS文件生成ZAR并且下载到控制器运行。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| Filename | sting | BAS文件路径 |
| run_mode | uint32 | 0-RAM  1-ROM |

**返回值：** 错误码。 type: int32

---

### ZAux_ZarDown

**描述：** 下载 ZAR 程序到控制器运行。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| Filename | sting | BAS 文件名带路径 |
| run_mode | int32 | 下载模式 0-RAM 1-ROM |

**返回值：** 错误码。    type:int32

---

### ZAux_SetRtcTime

**描述：** 设置RTC时间。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| RtcDate | sting | 系统日期 格式YYMMDD |
| RtcTime | sting | 系统时间 格式HHMMSS |

**返回值：** 错误码。    type:int32

---

## IO控制

### ZAux_Direct_GetIn

**描述：** 读取输入信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | IN编号 |

**返回值：** 错误码,输入口状态。 type: int32, uint32

---

### ZAux_Direct_SetOp

**描述：** 打开输出信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | 输出口编号 |
| iValue | uint32 | 输出口状态 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetOp

**描述：** 读取输出口状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | 输出口编号 |

**返回值：** 错误码,输出口状态。 type: int32,uint32

---

### ZAux_Direct_SetInvertIn

**描述：** 设置输入口反转。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | 输出口编号 |
| bifInvert | int | 反转状态 0/1 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetInvertIn

**描述：** 读取输入口反转状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | 输出口编号 |

**返回值：** 错误码,反转状态。 type: int32 ,int

---

### ZAux_Direct_GetInMulti

**描述：** 读取多个输入信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| startio | int | IO 口起始编号 |
| endio | int | IO 口结束编号 |

**返回值：** 错误码,按位获取的输入口的状态值。最多存储32 个输出口状态。    type:int32,int32

---

### ZAux_Direct_SetOutMulti

**描述：** IO 设置路输出状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iofirst | int | IO口起始编号 |
| ioend | int | IO口结束编号 |
| istate | int32 | 。输出口状态,istate 按位设置，一个 UNIT对应 32 个输出口状态(列表类型) |

**返回值：** 错误码,输出口状态。    type:int32

---

### ZAux_Direct_GetOutMulti

**描述：** IO 接口获取多路输出状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iofirst | int | IO口起始编号 |
| ioend | int | IO口结束编号 |

**返回值：** 错误码,输出口状态。    type:int32

---

## 模拟量控制

### ZAux_Direct_GetAD

**描述：** 读取模拟量输入信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | AIN口编号 |

**返回值：** 错误码,返回的模拟量值 4系列以下0-4095。 type: int32,folat

---

### ZAux_Direct_SetDA

**描述：** 打开模拟量输出信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | DA输出口编号 |
| fValue | float | 设定的模拟量值4系列以下0-4095 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetDA

**描述：** 读取模拟输出口状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | 模拟量输出口编号 |

**返回值：** 错误码,返回的模拟量值 4系列以下0-4095。 type: int32, float

---

### ZAux_Direct_SetDAC

**描述：** 设置模拟量输出 力矩、速度模式下可以  总线驱动需要设置对应DRIVE_PROFILE类型 与ATYPE

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| piValue | float | 模拟量 输出值 |

**返回值：** 错误码。    type:int32

---

### ZAux_Direct_GetDAC

**描述：** 读取模拟量输出 力矩、速度模式下可以  总线驱动需要设置对应DRIVE_PROFILE类型 与ATYPE。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,模拟量返回值。    type:int32,float

---

## PWM控制

### ZAux_Direct_SetPwmFreq

**描述：** 设置pwm频率。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | PWM编号口 |
| fValue | float | 频率 硬件PWM1M 软PWM 2K |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_SetPwmDuty

**描述：** 设置pwm占空比。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | PWM编号口 |
| fValue | float | 占空变       0-1  0表示关闭PWM口 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetPwmDuty

**描述：** 设置pwm占空比。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | PWM编号口 |

**返回值：** 错误码,返回的空比。 type: int32,float

---

### ZAux_Direct_GetPwmFreq

**描述：** 读取pwm频率。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionum | int | PWM编号口 |

**返回值：** 错误码,返回的频率。 type: int32,float

---

## 轴参数设置

### ZAux_Direct_SetAccel

**描述：** 设置加速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设定值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_SetParam

**描述：** 通用的参数修改函数 sParam: 填写参数名称。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| sParam | sting | 轴参数名称 "DPOS" ... |
| iaxis | int | 轴号 |
| fset | float | 设定值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetParam

**描述：** 通参数 通用的参数读取函数, sParam:填写参数名称。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| sParam | sting | 轴参数名称 "DPOS" ... |
| iaxis | int | 轴号 |

**返回值：** 错误码,读取的返回值。 type: int32,float

---

### ZAux_Direct_GetAccel

**描述：** 读取加速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| sParam | int | 轴号 |

**返回值：** 错误码,加速度返回值。 type: int32,float

---

### ZAux_Direct_SetDecel

**描述：** 设置减速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的减速度值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetDecel

**描述：** 读取减速度

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,设定的减速度返回值。  type: int32,float

---

### ZAux_Direct_SetSpeed

**描述：** 设置轴速度,单位为units/s,当多轴运动时,作为插补运动的速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的速度值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetSpeed

**描述：** 读取轴速度,单位为units/s,当多轴运动时,作为插补运动的速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的速度值。 type: int32,float

---

## 轴状态读取

### ZAux_Direct_GetAxisStatus

**描述：** 读取轴状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,轴状态返回值。  type: int32,int

---

### ZAux_Direct_GetAxisEnable

**描述：** 读取轴使能状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的使能状态。  type: int32,int

---

### ZAux_Direct_SetDpos

**描述：** 设置轴位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的坐标值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetDpos

**描述：** 读取轴位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的命令位置坐标。  type: int32,float

---

### ZAux_Direct_SetMpos

**描述：** 设置反馈位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的反馈位置 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetMpos

**描述：** 读取反馈位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的轴反馈位置坐标。    type: int32,float

---

### ZAux_Direct_GetMspeed

**描述：** 读取反馈速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的编码器反馈速度。   type: int32,float

---

## 运动控制

### ZAux_Direct_Move

**描述：** 多轴相对直线插补  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与轴数 |
| piAxislist | int | 轴列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveSp

**描述：** 相对多轴直线插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与轴数 |
| piAxislist | int | 轴列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveAbs

**描述：** 绝对多轴直线插补  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与轴数 |
| piAxislist | int | 轴列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveAbsSp

**描述：** 绝对多轴直线插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与轴数 |
| piAxislist | int | 轴列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Single_Cancel

**描述：** 单轴运动停止

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| imode | int | 停止模式 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Single_Vmove

**描述：** 单轴连续运动。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| idir | int | 方向 1正向 -1负向 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Single_Move

**描述：** 单轴相对运动。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fdistance | float | 距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Single_MoveAbs

**描述：** 单轴绝对运动。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fdistance | float | 距离 |

**返回值：** 错误码。 type: int32

---

## 插补运动

### ZAux_Direct_MoveCirc

**描述：** 相对圆心定圆弧插补运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveCircSp

**描述：** 相对圆心定圆弧插补运动 插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveCircAbs

**描述：** 绝对圆心圆弧插补运动  20130901 以后的控制器版本支持  无法画整圆。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveCircAbsSp

**描述：** 绝对圆心圆弧插补运动  20130901 以后的控制器版本支持  无法画整圆。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MultiMove

**描述：** 多条相对多轴直线插补 。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iMoveLen | int | 填写的运动长度 |
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。    type:int32

---

### ZAux_Direct_MultiMoveAbs

**描述：** 多条绝对多轴直线插补 。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iMoveLen | int | 填写的运动长度 |
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。    type:int32

---

## 回零控制

### ZAux_Direct_Single_Datum

**描述：** 控制器方式回零。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| imode | int | 模式 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetHomeStatus

**描述：** 回零完成状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

---

### ZAux_Direct_UserDatum

**描述：** 自定义二次回零。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| imode | int | 回零模式 |
| HighSpeed | float | 回零高速 |
| LowSpeed | float | 回零低速 |
| DatumOffset | float | 二次回零偏移距离 |

**返回值：** 错误码。    type:int32

---

## 总线控制

### ZAux_BusCmd_GetNodeNum

**描述：** 读取卡槽上节点数量。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| slot | int | 槽位号缺省0 |

**返回值：** 错误码， 返回扫描成功节点数量。type: int32,int

---

### ZAux_BusCmd_GetNodeInfo

**描述：** 读取节点上的信息。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| slot | int | 槽位号缺省0 |
| node | int | 节点编号0 |
| sel | int | 信息编号 0-厂商编号1-设备编号 2-设备版本 3-别名 10-IN个数 11-OUT个数 |

**返回值：** 错误码,返回信息。type: int32,int

---

### ZAux_BusCmd_GetNodeStatus

**描述：** 读取节点总线状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| slot | int | 槽位号缺省0 |
| node | int | 节点编号0 |

**返回值：** 错误码, 按位处理 bit0-节点是否存在  bit1-通讯状态   bit2-节点状态。type: int32,int

---

### ZAux_BusCmd_SDORead

**描述：** 读取节点SDO参数信息。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| slot | uint32 | 槽位号缺省0 |
| node | uint32 | 节点编号0 |
| index | uint32 | 对象字典编号(注意函数为10进制数据)0 |
| subindex | uint32 | 子编号     (注意函数为10进制数据) |
| aype | uint32 | 数据类型  1-bool 2-int8 3-int16 4-int32 5-uint8 6-uint16 7-uint32 |

**返回值：** 错误码, 读取的数据值: int32,int

---

### ZAux_BusCmd_SDOWrite

**描述：** 写节点SDO参数信息。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| slot | uint32 | 槽位号缺省0 |
| node | uint32 | 节点编号0 |
| index | uint32 | 对象字典编号(注意函数为10进制数据)0 |
| subindex | uint32 | 子编号     (注意函数为10进制数据) |
| aype | int | 数据类型  1-bool 2-int8 3-int16 4-int32 5-uint8 6-uint16 7-uint32 |
| Vvalue | uint32 | 设定的数据值 |

**返回值：** 错误码: int32

---

### ZAux_BusCmd_InitBus

**描述：** 总线初始化  (针对Zmotion tools 工具软件配置过总线参数控制器使用有效）。

**返回值：** 错误码。    type:int32

---

## 数据读写

### ZAux_Direct_SetTable

**描述：** 写table 。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| tabstart | int | 写入的TABLE起始编号 |
| numes | int | 写入的数量 |
| pfValue | float | 写入的数据值 |

**返回值：** 错误码。       type: int32

---

### ZAux_Direct_GetTable

**描述：** table读取, 可以一次读取多个。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| tabstart | int | 读取TABLE起始地址 |
| numes | int | 读取的数量 |

**返回值：** 错误码,返回的读取值，多个时必须分配空间。 type: int32,float

---

### ZAux_Direct_SetUserArray

**描述：** 设置BASIC自定义全局数组。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| arrayname | sting | 数组名称 |
| arraystart | int | 数组起始元素 us单位 |
| numes | int | 元素数量 |
| pfValue | float | 设置值 |

**返回值：** 错误码。             type: int32

---

### ZAux_Direct_GetUserArray

**描述：** 读取设置BASIC自定义全局数组 , 可以一次读取多个。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| arrayname | sting | 数组名称 |
| arraystart | int | 数组起始元素 us单位 |
| numes | int | 元素数量 |

**返回值：** 错误码,读取数组元素的值多个时必须分配空间。  type: int32,float

---

### ZAux_Direct_SetUserVar

**描述：** 设置自定义变量。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| varname | sting | 变量名称字符串 |
| pfValue | float | 变量值 |

**返回值：** 错误码。             type: int32

---

### ZAux_Direct_GetUserVar

**描述：** 读取自定义全局变量。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| varname | sting | 变量名称字符串 |

**返回值：** 错误码,变量值。             type: int32,float

---

## 其他功能

### ZAux_SetComDefaultBaud

**描述：** 可以修改缺省的波特率等设置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| dwBaudRate | uint32 | 波特率 |
| dwParity | uint32 | NOPARITY,校验位 |
| dwStopBits | uint32 | ONESTOPBIT停止位 |

**返回值：** 错误码。 type: int32

---

### ZAux_SetIp

**描述：** 修改控制器IP地址。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ipaddress | sting | IP地址 |

**返回值：** 错误码。 type: int32

---

### ZAux_GetModbusIn

**描述：** 参数 快速读取多个输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionumfirst | int | IN起始编号 |
| ionumend | int | IN结束编号 |

**返回值：** 错误码,位状态按位存储。 type: int32,uint8

---

### ZAux_GetModbusOut

**描述：** 参数 快速读取多个当前的输出状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ionumfirst | int | IN起始编号 |
| ionumend | int | IN结束编号 |

**返回值：** 错误码,位状态按位存储。 type: int32,uint8

---

### ZAux_GetModbusDpos

**描述：** 参数 快速读取多个当前的DPOS。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 轴数量 |

**返回值：** 错误码,读取的坐标值从轴0开始。 type: int32,float

---

### ZAux_GetModbusMpos

**描述：** 参数 快速读取多个当前的MPOS。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 轴数量 |

**返回值：** 错误码, 读取的反馈坐标值 从轴0开始。 type: int32,float

---

### ZAux_GetModbusCurSpeed

**描述：** 参数 快速读取多个当前的速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 轴数量 |

**返回值：** 错误码,读取的当前速度 从轴0开始。 type: int32,float

---

### ZAux_Direct_GetAddax

**描述：** 读取叠加轴。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,读取的轴叠加轴号。  type: int32,float

---

### ZAux_Direct_SetAlmIn

**描述：** 设置轴告警信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 报警信号输入口编号，取消时设定-1 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetAlmIn

**描述：** 读取告警信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,报警信号输入口返回值。  type: int32,int

---

### ZAux_Direct_SetAtype

**描述：** 设置轴类型。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 轴类型 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetAtype

**描述：** 读取轴类型。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,轴类型返回值。  type: int32,int

---

### ZAux_Direct_SetAxisAddress

**描述：** 设置轴地址。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| piValue | int | 轴地址设定值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetAxisAddress

**描述：** 读取轴地址。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,轴地址返回值。  type: int32,int

---

### ZAux_Direct_SetAxisEnable

**描述：** 设置轴使能 （只针对总线控制器轴使用有效）。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 状态 0-关闭 1- 打开 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_SetClutchRate

**描述：** 设置链接速率。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 同步连接速率 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetClutchRate

**描述：** 读取链接速率。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,连接速率返回值。  type: int32,float

---

### ZAux_Direct_SetCloseWin

**描述：** 设置锁存触发的结束坐标范围点。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设定的范围值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetCloseWin

**描述：** 读取锁存触发的结束坐标范围点。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的范围值。  type: int32,float

---

### ZAux_Direct_SetCornerMode

**描述：** 设置拐角减速。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 拐角减速模式 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetCornerMode

**描述：** 读取拐角减速。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的拐角模式。  type: int32,int

---

### ZAux_Direct_SetCreep

**描述：** 设置回零爬行速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的速度值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetCreep

**描述：** 读取回零爬行速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的爬行速度值。  type: int32,float

---

### ZAux_Direct_SetDatumIn

**描述：** 设置原点信号   设定-1为取消原点设置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设置的原点信号输入口编号 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetDatumIn

**描述：** 读取原点信号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回原点输入口编号。  type: int32,int

---

### ZAux_Direct_SetDecelAngle

**描述：** 设置拐角减速角度，开始减速角度，单位为弧度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的拐角减速角度 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetDecelAngle

**描述：** 读取拐角开始减速角度，单位为弧度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的拐角减速角度。  type: int32,float

---

### ZAux_Direct_GetEncoder

**描述：** 读取内部编码器值  （总线绝对值伺服时为绝对值位置）。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的内部编码器值。  type: int32,float

---

### ZAux_Direct_GetEndMove

**描述：** 读取当前运动的最终位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的最终位置。  type: int32,float

---

### ZAux_Direct_GetEndMoveBuffer

**描述：** 读取当前和缓冲中运动的最终位置，可以用于相对绝对转换。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的最终位置。  type: int32,float

---

### ZAux_Direct_SetEndMoveSpeed

**描述：** 设置SP运动的结束速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设定的速度值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetEndMoveSpeed

**描述：** 读取SP运动的结束速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的速度值。  type: int32,float

---

### ZAux_Direct_SetErrormask

**描述：** 设置错误标记,和AXISSTATUS做与运算来决定哪些错误需要关闭WDOG。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设置值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetErrormask

**描述：** 读取错误标记,和AXISSTATUS做与运算来决定哪些错误需要关闭WDOG。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的标记值。  type: int32,int

---

### ZAux_Direct_SetFastJog

**描述：** 设置快速JOG输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 快速JOG输入口编号 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFastJog

**描述：** 读取快速JOG输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的JOG输入口编号。  type: int32,int

---

### ZAux_Direct_SetFastDec

**描述：** 设置快速减速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设定的快速减速度 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFastDec

**描述：** 读取快速减速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的快速减速度。  type: int32,float

---

### ZAux_Direct_GetFe

**描述：** 读取随动误差。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的随动误差。  type: int32,float

---

### ZAux_Direct_SetFeLimit

**描述：** 设置最大允许的随动误差值。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的最大误差值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFeLimit

**描述：** 读取最大允许的随动误差值。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的设置最大误差值。  type: int32,float

---

### ZAux_Direct_SetFRange

**描述：** 设置报警时随动误差值。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的误差值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFeRange

**描述：** 读取报警时的随动误差值。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的报警误差值。  type: int32,float

---

### ZAux_Direct_SetFholdIn

**描述：** 设置保持输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | int | 设置的输入口编号 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFholdIn

**描述：** 读取保持输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回输入HOLDIN输入口编号。  type: int32,int

---

### ZAux_Direct_SetFhspeed

**描述：** 设置轴保持速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的速度值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFhspeed

**描述：** 读取轴保持速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的保持速度。  type: int32,int

---

### ZAux_Direct_SetForceSpeed

**描述：** 设置SP运动的运行速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的速度值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetForceSpeed

**描述：** 读取SP运动的运行速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回SP运动速度值。  type: int32,int

---

### ZAux_Direct_SetFsLimit

**描述：** 设置正向软限位,取消时设置一个较大值即可。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设定的限位值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFsLimit

**描述：** 读取正向软限位。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的正向限位坐标。  type: int32,float

---

### ZAux_Direct_SetFullSpRadius

**描述：** 设置小圆限速最小半径。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的最小半径 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFullSpRadius

**描述：** 读取小圆限速最小半径。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的限速半径。  type: int32,float

---

### ZAux_Direct_SetFwdIn

**描述：** 设置正向硬限位输入  设置成-1时表示不设置限位。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设置的限位输入口编号 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFwdIn

**描述：** 读取正向硬限位输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回正向限位输入口编号。  type: int32,float

---

### ZAux_Direct_SetFwdJog

**描述：** 设置正向JOG输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设置的JOG输入口编号 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetFwdJog

**描述：** 读取正向JOG输入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的JOG输入口编号。  type: int32,int

---

### ZAux_Direct_GetIfIdle

**描述：** 读取轴是否运动结束。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回运行状态 0-运动中 -1 停止。  type: int32,int

---

### ZAux_Direct_SetInvertStep

**描述：** 设置脉冲输出模式。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设定的脉冲输出模式 脉冲+方向/双脉冲 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetInvertStep

**描述：** 读取脉冲输出模式。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的脉冲模式。  type: int32,int

---

### ZAux_Direct_SetInterpFactor

**描述：** 设置插补时轴是否参与速度计算,缺省参与(1)。此参数只对直线和螺旋的第三个轴起作用。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 模式 0-不参数 1-参与 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetInterpFactor

**描述：** 读取插补时轴是否参与速度计算，缺省参与(1)。此参数只对直线和螺旋的第三个轴起作用。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的速度计算模式。  type: int32,int

---

### ZAux_Direct_SetJogSpeed

**描述：** 设置JOG时速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设定的速度值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetJogSpeed

**描述：** 读取插补时轴是否参与速度计算，缺省参与(1)。此参数只对直线和螺旋的第三个轴起作用。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的速度计算模式。  type: int32,int

---

### ZAux_Direct_GetLoaded

**描述：** 读取当前除了当前运动是否还有缓冲。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回状态值  -1 没有剩余函数 0-还有剩余运动。  type: int32,int

---

### ZAux_Direct_GetLinkax

**描述：** 读取当前链接运动的参考轴号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回链接的参考轴号。  type: int32,int

---

### ZAux_Direct_SetLspeed

**描述：** 设置轴起始速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设定的速度值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetLspeed

**描述：** 读取轴起始速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的起始速度值。  type: int32,float

---

### ZAux_Direct_SetHomeWait

**描述：** 设置回零反找等待时间。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | int | 回零反找等待时间 MS |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetHomeWait

**描述：** 读取回零反找等待时间。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的反找等待时间。  type: int32,int

---

### ZAux_Direct_GetMark

**描述：** 读取编码器锁存示教返回状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的锁存触发状态 -1-锁存触发 0-未触发。  type: int32,int

---

### ZAux_Direct_GetMarkB

**描述：** 读取编码器锁存b返回状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的锁存触发状态 -1-锁存触发 0-未触发。  type: int32,int

---

### ZAux_Direct_SetMaxSpeed

**描述：** 设置脉冲输出最高频率。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设置的最高脉冲频率 |

**返回值：** 错误码。  type: int32,int

---

### ZAux_Direct_GetMaxSpeed

**描述：** 读取脉冲输出最高频率。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的脉冲频率。    type:int32,int

---

### ZAux_Direct_SetMerge

**描述：** 设置连续插补。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 连续插补开关 0-关闭连续插补 1-打开连续插补 |

**返回值：** 错误码,返回的反找等待时间。  type: int32,int

---

### ZAux_Direct_GetMerge

**描述：** 读取连续插补状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的连续插补开关状态。    type: int32,int

---

### ZAux_Direct_GetMovesBuffered

**描述：** 读取当前被缓冲起来的运动个数。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,缓冲运动数。    type: int32,int

---

### ZAux_Direct_GetMoveCurmark

**描述：** 读取当前正在运动指令的MOVE_MARK标号。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,当前MARK标号。    type: int32,int

---

### ZAux_Direct_SetMovemark

**描述：** 设置运动指令的MOVE_MARK标号 每当有运动进入轴运动缓冲时MARK自动+1。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设定的MARK值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetMtype

**描述：** 读取当前正在运动指令类型。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回当前的运动类型。    type: int32,int

---

### ZAux_Direct_SetOffpos

**描述：** 设置修改偏移位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的反馈位置 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetOffpos

**描述：** 读取修改偏移位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的偏移坐标值    type: int32,float

---

### ZAux_Direct_SetOpenWin

**描述：** 设置锁存触发的结束坐标范围点。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的坐标值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetOpenWin

**描述：** 读取锁存触发的结束坐标范围点。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的结束坐标值。 type: int32,float

---

### ZAux_Direct_GetRegPos

**描述：** 读取返回锁存的测量反馈位置(MPOS)。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,锁存的坐标位置。 type: int32,float

---

### ZAux_Direct_GetRegPosB

**描述：** 读取返回锁存的测量反馈位置(MPOS)。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,锁存的坐标位置。 type: int32,float

---

### ZAux_Direct_GetRemain

**描述：** 读取返回轴当前运动还未完成的距离。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的剩余距离。 type: int32,float

---

### ZAux_Direct_GetRemain_LineBuffer

**描述：** 参数  轴剩余的缓冲, 按直线段来计算。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,剩余的直线缓冲数量。 type: int32,int

---

### ZAux_Direct_GetRemain_Buffer

**描述：** 参数  轴剩余的缓冲, 按最复杂的空间圆弧来计算。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,剩余的缓冲数量。 type: int32,int

---

### ZAux_Direct_SetRepDist

**描述：** 设置锁存触发的结束坐标范围点。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的坐标值 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetRepDist

**描述：** 读取根据REP_OPTION设置来自动循环轴DPOS和MPOS坐标。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的循环坐标值。 type: int32,float

---

### ZAux_Direct_SetRepOption

**描述：** 设置坐标重复设置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 模式 |

**返回值：** 错误码。  type: int32

---

### ZAux_Direct_GetRepOption

**描述：** 读取坐标重复设置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的模式。 type: int32,int

---

### ZAux_Direct_SetRevIn

**描述：** 设置负向硬件限位开关对应的输入点编号，-1无效。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设置的输入口编号 |

**返回值：** 错误码。          type: int32

---

### ZAux_Direct_GetRevIn

**描述：** 读取负向硬件限位开关对应的输入点编号，-1无效。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的负向限位输入口编号。 type: int32,int

---

### ZAux_Direct_SetRevJog

**描述：** 设置负向JOG输入对应的输入点编号,-1无效。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iValue | int | 设置的输入口编号 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetRevJog

**描述：** 读取负向JOG输入对应的输入点编号,-1无效。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的输入口编号。 type: int32,int

---

### ZAux_Direct_SetRsLimit

**描述：** 设置负向软限位位置。  设置一个较大的值时认为取消限位。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 负向限位值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetRsLimit

**描述：** 读取负向软限位位置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,设定的限位值。 type: int32,float

---

### ZAux_Direct_SetSramp

**描述：** 设置 S曲线设置。 0-梯形加减速。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | S曲线平滑时间MS |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetSramp

**描述：** 读取 S曲线设置。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,平滑时间。 type: int32,float

---

### ZAux_Direct_SetStartMoveSpeed

**描述：** 设置 自定义速度的SP运动的起始速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的速度值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetStartMoveSpeed

**描述：** 读取自定义速度的SP运动的起始速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的SP运动起始速度值。 type: int32,float

---

### ZAux_Direct_SetStopAngle

**描述：** 设置 减速到最低的最小拐角 弧度制。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的角度值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetStopAngle

**描述：** 取减速到最低的最小拐角 弧度制。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的拐角停止角度。 type: int32,float

---

### ZAux_Direct_SetZsmooth

**描述：** 设置 减速倒角半径。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 倒角半径 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetZsmooth

**描述：** 读取倒角半径。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的倒角半径值。 type: int32,float

---

### ZAux_Direct_SetUnits

**描述：** 设置 脉冲当量。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| fValue | float | 设置的当量值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetUnits

**描述：** 读取脉冲当量。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的脉冲当量。 type: int32,float

---

### ZAux_Direct_GetVectorBuffered

**描述：** 读取返回轴当前当前运动和缓冲运动还未完成的距离。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的剩余的距离。 type: int32,float

---

### ZAux_Direct_GetVpSpeed

**描述：** 读取当前轴运行的命令速度。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的当前速度值。 type: int32,float

---

### ZAux_Direct_GetVariablef

**描述：** 全局变量读取, 也可以是参数等等。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| pname | string | 全局变量名称/或者指定轴号的轴参数名称DPOS(0) |

**返回值：** 错误码,返回值。 type: int32,float

---

### ZAux_Direct_GetVariableInt

**描述：** 全局变量读取, 也可以是参数等等。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| pname | string | 全局变量名称/或者指定轴号的轴参数名称DPOS(0) |

**返回值：** 错误码,返回值。 type: int32,int

---

### ZAux_Direct_Base

**描述：** BASE指令调用

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与轴数 |
| piAxislist | int | 轴列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Defpos

**描述：** 定义DPOS,不建议使用,可以直接调用SETDPOS达到同样效果。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| pfDpos | float | 设置的坐标值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveModify

**描述：** 运动中修改结束位置  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| pfDisance | float | 绝对距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveCirc2

**描述：** 相对3点定圆弧插补运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点，相对起始点距离 |
| fmid2 | float | 第二个轴中间点，相对起始点距离 |
| fend1 | float | 第一个轴结束点，相对起始点距离 |
| fend2 | float | 第二个轴结束点，相对起始点距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveCirc2Abs

**描述：** 绝对3点定圆弧插补运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点，相对起始点距离 |
| fmid2 | float | 第二个轴中间点，相对起始点距离 |
| fend1 | float | 第一个轴结束点，相对起始点距离 |
| fend2 | float | 第二个轴结束点，相对起始点距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveCirc2Sp

**描述：** 相对3点定圆弧插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点，相对起始点距离 |
| fmid2 | float | 第二个轴中间点，相对起始点距离 |
| fend1 | float | 第一个轴结束点，相对起始点距离 |
| fend2 | float | 第二个轴结束点，相对起始点距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveCirc2AbsSp

**描述：** 绝对3点定圆弧插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点，相对起始点距离 |
| fmid2 | float | 第二个轴中间点，相对起始点距离 |
| fend1 | float | 第一个轴结束点，相对起始点距离 |
| fend2 | float | 第二个轴结束点，相对起始点距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelical

**描述：** 相对3轴圆心螺旋插补运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fDistance3 | float | 第三个轴运动距离 |
| imode | int | 第三轴的速度计算:0(缺省)第三轴参与速度计算。1第三轴不参与速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelicalAbs

**描述：** 绝对3轴圆心螺旋插补运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fDistance3 | float | 第三个轴运动距离 |
| imode | int | 第三轴的速度计算:0(缺省)第三轴参与速度计算。1第三轴不参与速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelicalSp

**描述：** 相对3轴圆心螺旋插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fDistance3 | float | 第三个轴运动距离 |
| imode | int | 第三轴的速度计算:0(缺省)第三轴参与速度计算。1第三轴不参与速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelicalAbsSp

**描述：** 绝对3轴圆心螺旋插补运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第一个轴运动坐标 |
| fend2 | float | 第二个轴运动坐标 |
| fcenter1 | float | 第一个轴运动圆心，相对与起始点 |
| fcenter2 | float | 第二个轴运动圆心，相对与起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fDistance3 | float | 第三个轴运动距离 |
| imode | int | 第三轴的速度计算:0(缺省)第三轴参与速度计算。1第三轴不参与速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelical2

**描述：** 相对3轴 3点画螺旋插补运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点 |
| fmid2 | float | 第二个轴中间点 |
| fend1 | float | 第一个轴结束点 |
| fend2 | float | 第二个轴结束点 |
| fDistance3 | float | 第三个轴运动距离 |
| imode | int | 第三轴的速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelical2Abs

**描述：** 绝对3轴 3点画螺旋插补运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点 |
| fmid2 | float | 第二个轴中间点 |
| fend1 | float | 第一个轴结束点 |
| fend2 | float | 第二个轴结束点 |
| fDistance3 | float | 第三个轴运动结束点 |
| imode | int | 第三轴的速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelical2Sp

**描述：** 相对3轴 3点画螺旋插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点 |
| fmid2 | float | 第二个轴中间点 |
| fend1 | float | 第一个轴结束点 |
| fend2 | float | 第二个轴结束点 |
| fDistance3 | float | 第三个轴运动距离 |
| imode | int | 第三轴的速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MHelical2AbsSp

**描述：** 绝对3轴 3点画螺旋插补SP运动  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fmid1 | float | 第一个轴中间点 |
| fmid2 | float | 第二个轴中间点 |
| fend1 | float | 第一个轴结束点 |
| fend2 | float | 第二个轴结束点 |
| fDistance3 | float | 第三个轴运动距离 |
| imode | int | 第三轴的速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipse

**描述：** 相对椭圆插补 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipseAbs

**描述：** 绝对椭圆插补 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipseSp

**描述：** 相对椭圆插补SP运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipseAbsSp

**描述：** 绝对椭圆插补SP运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipseHelical

**描述：** 相对 椭圆 + 螺旋插补运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |
| fDistance3 | float | 第三个轴的运动距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipseHelicalAbs

**描述：** 绝对椭圆 + 螺旋插补运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |
| fDistance3 | float | 第三个轴的运动距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipseHelicalSp

**描述：** 相对 椭圆 + 螺旋插补SP运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |
| fDistance3 | float | 第三个轴的运动距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MEclipseHelicalAbsSp

**描述：** 绝对椭圆 + 螺旋插补SP运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 终点第一个轴运动坐标，相对于起始点 |
| fend2 | float | 终点第二个轴运动坐标，相对于起始点 |
| fcenter1 | float | 中心第一个轴运动坐标，相对于起始点 |
| fcenter2 | float | 中心第二个轴运动坐标，相对于起始点 |
| idirection | int | 0-逆时针,1-顺时针 |
| fADis | float | 第一轴的椭圆半径，半长轴或者半短轴都可 |
| fBDis | float | 第二轴的椭圆半径,半长轴或者半短轴都可,AB相等时自动为圆弧或螺 |
| fDistance3 | float | 第三个轴的运动距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MSpherical

**描述：** 空间圆弧 + 螺旋插补运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第1个轴运动距离参数1        相对与起点 |
| fend2 | float | 第2个轴运动距离参数1        相对与起点 |
| fend3 | float | 第3个轴运动距离参数1        相对与起点 |
| fcenter1 | float | 第1个轴运动距离参数2    相对与起点 |
| fcenter2 | float | 第2个轴运动距离参数2    相对与起点 |
| fcenter3 | float | 第3个轴运动距离参数2   相对与起点 |
| imode | int | 指定前面参数的意义 |
| fcenter4 | float | 第4个轴运动距离参数 |
| fcenter5 | float | 第5个轴运动距离参数 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MSphericalSp

**描述：** 空间圆弧 + 螺旋 插补SP运动 20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| fend1 | float | 第1个轴运动距离参数1        相对与起点 |
| fend2 | float | 第2个轴运动距离参数1        相对与起点 |
| fend3 | float | 第3个轴运动距离参数1        相对与起点 |
| fcenter1 | float | 第1个轴运动距离参数2    相对与起点 |
| fcenter2 | float | 第2个轴运动距离参数2    相对与起点 |
| fcenter3 | float | 第3个轴运动距离参数2   相对与起点 |
| imode | int | 指定前面参数的意义 |
| fcenter4 | float | 第4个轴运动距离参数 |
| fcenter5 | float | 第5个轴运动距离参数 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveSpiral

**描述：** 渐开线圆弧插补运动,相对移动方式,当起始半径0直接扩散时从0角度开始。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| centre1 | float | 第1轴圆心的相对距离 |
| centre2 | float | 第2轴圆心的相对距离 |
| circles | float | 要旋转的圈数，可以为小数圈，负数表示顺时针 |
| pitch | float | 每圈的扩散距离，可以为负 |
| distance3 | float | 第3轴螺旋的功能,指定第3轴的相对距离,此轴不参与速度计算 |
| distance4 | float | 第4轴螺旋的功能,指定第4轴的相对距离,此轴不参与速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveSpiralSp

**描述：** 渐开线圆弧插补SP运动,相对移动方式,当起始半径0直接扩散时从0角度开始。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| centre1 | float | 第1轴圆心的相对距离 |
| centre2 | float | 第2轴圆心的相对距离 |
| circles | float | 要旋转的圈数，可以为小数圈，负数表示顺时针 |
| pitch | float | 每圈的扩散距离，可以为负 |
| distance3 | float | 第3轴螺旋的功能,指定第3轴的相对距离,此轴不参与速度计算 |
| distance4 | float | 第4轴螺旋的功能,指定第4轴的相对距离,此轴不参与速度计算 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveSmooth

**描述：** 空间直线运动，根据下一个直线运动的绝对坐标在拐角自动插入圆弧，加入圆弧后会使得运动的终点与直线的终点不一致，拐角过大时不会插入圆弧，当距离不够时会自动减小半径。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| end1 | float | 第1个轴运动绝对坐标 |
| end2 | float | 第2个轴运动绝对坐标 |
| end3 | float | 第3个轴运动绝对坐标 |
| next1 | float | 第1个轴下一个直线运动绝对坐标 |
| next2 | float | 第2个轴下一个直线运动绝对坐标 |
| next3 | float | 第3个轴下一个直线运动绝对坐标 |
| radius | float | 插入圆弧的半径，当过大的时候自动缩小 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveSmoothSp

**描述：** 空间直线插补SP运动,根据下一个直线运动的绝对坐标在拐角自动插入圆弧,加入圆弧后会使得运动的终点与直线的终点不一致,拐角过大时不会插入圆弧,当距离不够时会自动减小半径。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| end1 | float | 第1个轴运动绝对坐标 |
| end2 | float | 第2个轴运动绝对坐标 |
| end3 | float | 第3个轴运动绝对坐标 |
| next1 | float | 第1个轴下一个直线运动绝对坐标 |
| next2 | float | 第2个轴下一个直线运动绝对坐标 |
| next3 | float | 第3个轴下一个直线运动绝对坐标 |
| radius | float | 插入圆弧的半径，当过大的时候自动缩小 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MovePause

**描述：** 运动暂停               ，插补运动暂停主轴。轴列表轴第一个轴。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| imode | int | 模式 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveResume

**描述：** 取消运动暂停。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveLimit

**描述：** 在当前的运动末尾位置增加速度限制，用于强制拐角减速。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| limitspeed | float | 限制到的速度 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveOp

**描述：** 在运动缓冲中加入输出指令。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| ioutnum | int | 输出口编号 |
| ivalue | int | 输出口状态 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveOpMulti

**描述：** 在运动缓冲中加入连续输出口输出指令。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| ioutnumfirst | int | 输出口起始编号 |
| ioutnumend | int | 输出口结束编号 |
| ivalue | int | 对应输出口状态二进制组合值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveOp2

**描述：** 在运动缓冲中加入输出指令 ,指定时间后输出状态翻转。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| ioutnum | int | 输出口起始编号 |
| ivalue | int | 输出口状态 |
| iofftimems | int | 状态反转时间 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveAout

**描述：** 在运动缓冲中加入输出指令 ,指定时间后输出状态翻转。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| ioutnum | int | 输出口起始编号 |
| ivalue | int | 输出口状态 |
| iofftimems | int | 状态反转时间 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveDelay

**描述：** 在运动缓冲中加入延时指令。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| itimems | int | 延时时间 itimems 毫秒 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveTurnabs

**描述：** 旋转台直线插补运动。  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| tablenum | int | 存储旋转台参数的table编号 |
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_McircTurnabs

**描述：** 旋转台圆弧+螺旋插补运动。  20130901 以后的控制器版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| tablenum | int | 存储旋转台参数的table编号 |
| refpos1 | float | 第一个轴参考点，绝对位置 |
| refpos2 | float | 第一个轴参考点，绝对位置 |
| mode | int | 1-参考点是当前点前面,2-参考点是结束点后面,3-参考点在中间，采用三点定圆的方式 |
| end1 | float | 第一个轴结束点，绝对位置 |
| end2 | float | 第二个轴结束点，绝对位置 |
| imaxaxises | int | 参与运动轴数量 |
| piAxislist | int | 轴列表 |
| pfDisancelist | float | 螺旋轴距离列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Cam

**描述：** 电子凸轮 同步运动。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| istartpoint | int | 起始点TABLE编号 |
| iendpoint | int | 结束点TABLE编号 |
| ftablemulti | float | 位置比例，一般设为脉冲当量值 |
| fDistance | float | 参考运动的距离，用来计算总运动时间 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Cambox

**描述：** 电子凸轮 同步运动。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| istartpoint | int | 起始点TABLE编号 |
| iendpoint | int | 结束点TABLE编号 |
| ftablemulti | float | 位置比例，一般设为脉冲当量值 |
| fDistance | float | 参考运动的距离，用来计算总运动时间 |
| ilinkaxis | int | 参考主轴 |
| ioption | int | 参考轴的连接方式 |
| flinkstartpos | float | ioption条件中距离参数 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Movelink

**描述：** 电子凸轮 同步运动。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 参与运动的轴号(跟随轴) |
| fDistance | float | 同步过程跟随轴运动距离 |
| fLinkDis | float | 同步过程参考轴(主轴)运动绝对距离 |
| fLinkAcc | float | 跟随轴加速阶段，参考轴移动的绝对距离 |
| fLinkDec | float | 跟随轴减速阶段，参考轴移动的绝对距离 |
| iLinkaxis | int | 参考轴的轴号 |
| ioption | int | 连接模式选项 |
| flinkstartpos | float | 连接模式选项中运动距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Moveslink

**描述：** 特殊凸轮 同步运动。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 参与运动的轴号(跟随轴) |
| fDistance | float | 同步过程跟随轴运动距离 |
| fLinkDis | float | 同步过程参考轴(主轴)运动绝对距离 |
| fLinkAcc | float | 启动时跟随轴和参考轴的速度比例,units/units单位,负数表示跟随轴负向运动 |
| fLinkDec | float | 结束时跟随轴和参考轴的速度比例,units/units单位, 负数表示跟随轴负向运动 |
| iLinkaxis | int | 参考轴的轴号 |
| ioption | int | 连接模式选项 |
| flinkstartpos | float | 连接模式选项中运动距离 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Connect

**描述：** 连接 同步运动指令 电子齿轮。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ratio | flot | 比率，可正可负，注意是脉冲个数的比例 |
| link_axis | int | 连接轴的轴号，手轮时为编码器轴 |
| move_axis | int | 随动轴号 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Connpath

**描述：** 连接 同步运动指令 电子齿轮 将当前轴的目标位置与link_axis轴的插补矢量长度通过电子齿轮连接。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| ratio | int | 比率，可正可负，注意是脉冲个数的比例 |
| link_axis | int | 连接轴的轴号，手轮时为编码器轴 |
| move_axis | int | 随动轴号 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Regist

**描述：** 连接 同步运动指令 电子齿轮 将当前轴的目标位置与link_axis轴的插补矢量长度通过电子齿轮连接。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| imode | int | 锁存模式 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_EncoderRatio

**描述：** 编码器输入齿轮比，缺省(1,1)。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| output_count | int | 分子,不要超过65535 |
| input_count | int | 分母,不要超过65535 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_StepRatio

**描述：** 设置步进输出齿轮比，缺省(1,1)。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| output_count | int | 分子,1-65535 |
| input_count | int | 分母,1-65535 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Rapidstop

**描述：** 所有轴立即停止。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imode | int | 停止模式 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_CancelAxisList

**描述：** 多个轴运动停止。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| imaxaxises | int | 轴数 |
| piAxislist | int | 轴列表 |
| imode | int | 停止模式 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Connframe

**描述：** CONNFRAME机械手逆解指令   2系列以上控制器支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| Jogmaxaxises | int | 关节轴数量 |
| JogAxislist | int | 关节轴列表 |
| frame | int | 机械手类型 |
| tablenum | int | 机械手参数TABLE起始编号 |
| Virmaxaxises | int | 关联虚拟轴个数 |
| VirAxislist | int | 虚拟轴列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Connreframe

**描述：** CONNREFRAME机械手正解指令 2系列以上控制器支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| Virmaxaxises | int | 关联虚拟轴个数 |
| VirAxislist | int | 虚拟轴列表 |
| frame | int | 机械手类型 |
| tablenum | int | 机械手参数TABLE起始编号 |
| Jogmaxaxises | int | 关节轴数量 |
| JogAxislist | int | 关节轴列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_Single_Addax

**描述：** 轴叠加运动      iaddaxis运动叠加到iaxis轴 ,ADDAX指令叠加的是脉冲个数。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 被叠加轴 |
| iaddaxis | int | 叠加轴 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_SetVrf

**描述：** 写VR。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| vrstartnum | int | VR起始编号 |
| numes | int | 写入的数量 |
| pfValue | float | 写入的数据列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_GetVrf

**描述：** VR读取, 可以一次读取多个。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| vrstartnum | int | VR起始编号 |
| numes | int | 写入的数量 |

**返回值：** 错误码,返回的读取值，多个时必须分配空间。 type: int32,float

---

### ZAux_Direct_GetVrInt

**描述：** VRINT读取, 必须150401以上版本才支持VRINT的DIRECTCOMMAND读取。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| vrstartnum | int | VR起始编号 |
| numes | int | 读取的数量 |

**返回值：** 错误码,返回的读取值，多个时必须分配空间。 type: int32,float

---

### ZAux_TransStringtoFloat

**描述：** 字符串转为float。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| pstringin | sting | 数据的字符串 |
| inumes | int | 转换数据个数 |

**返回值：** 错误码,转换的数据。 type: int32,float

---

### ZAux_TransStringtoInt

**描述：** 字符串转为int。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| pstringin | sting | 数据的字符串 |
| inumes | int | 转换数据个数 |

**返回值：** 错误码,转换的数据。 type: int32,int

---

### ZAux_WriteUFile

**描述：** 把float格式的变量列表存储到文件,与控制器的U盘文件格式一致。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| sFilename | sting | 文件绝对路径 |
| pVarlist | float | 写入的数据列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_ReadUFile

**描述：** 读取float格式的变量列表,与控制器的U盘文件格式一致。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| sFilename | sting | 文件绝对路径 |

**返回值：** 错误码,读取的数据列表。 type: int32,int

---

### ZAux_Modbus_Set0x

**描述：** modbus寄存器操作 modbus_bit。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |
| pdata | uint8 | 设置的位状态(列表类型) |

**返回值：** 错误码。 type: uint16

---

### ZAux_Modbus_Get0x

**描述：** modbus寄存器操作 modbus_bit。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |

**返回值：** 错误码,返回的位状态  按位存储。 type: int32,uint8

---

### ZAux_Modbus_Set4x

**描述：** modbus寄存器操作 MODBUS_REG。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |
| inum | uint16 | 设置值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Modbus_Get4x

**描述：** modbus寄存器操作  MODBUS_REG。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |

**返回值：** 错误码,读取的REG寄存器值。 type: int32,uint16

---

### ZAux_Modbus_Get4x_Float

**描述：** Modbus 寄存器操作 MODBUS_IEEE 读取。               MODBUS_IEEE。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |

**返回值：** 错误码,读取的REG寄存器值。 type: int32,float

---

### ZAux_Modbus_Set4x_Float

**描述：** :modbus寄存器操作。              MODBUS_IEEE

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |
| pdata | float | 数据列表 |

**返回值：** 错误码。 type: int32

---

### ZAux_Modbus_Get4x_Long

**描述：** :modbus寄存器操作               MODBUS_LONG。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |

**返回值：** 错误码,读取的REG寄存器值。 type: int32,int32

---

### ZAux_Modbus_Set4x_Long

**描述：** :modbus寄存器操作               MODBUS_LONG。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | 起始编号 |
| inum | uint16 | 数量 |
| inum | int32 | 设置值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Modbus_Set4x_String

**描述：** 设置modbus_string。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | modbus起始地址 |
| inum | uint16 | 长度 |
| pdata | sting | 写入的字符串 |

**返回值：** 错误码。 type: int32

---

### ZAux_Modbus_Get4x_String

**描述：** 读取modbus_string。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| start | uint16 | modbus起始地址 |
| inum | uint16 | 长度 |

**返回值：** 错误码,读取返回的字符串。 type: int32,sting

---

### ZAux_FlashWritef

**描述：** 写用户flash块, float数据。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| uiflashid | uint16 | modbus起始地址 |
| uinumes | int32 | 变量个数 |
| pfvlue | float | 数据列表 |

**返回值：** 错误码。 type: int32,int32

---

### ZAux_FlashReadf

**描述：** 读取用户flash块, float数据。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| uiflashid | uint16 | flash块号 |
| uinumes | int32 | 缓冲变量个数 |

**返回值：** 错误码,读取到的变量个数。 type: int32,int32

---

### ZAux_Trigger

**描述：** 示波器触发函数 150723以后固件版本支持。

---

### ZAux_Direct_MovePara

**描述：** 运动中修改参数. 20170503以上固件支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| base_axis | uint32 | 运动轴轴号 |
| paraname | sting | 参数名称字符串 |
| iaxis | uint32 | 修改参数的轴号 |
| fvalue | float | 参数设定值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MovePwm

**描述：** 运动中修改PWM 20170503以上固件支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| base_axis | uint32 | 运动轴轴号 |
| pwm_num | uint32 | PWM 口编号 |
| pwm_duty | float | 设定的占空比 |
| pwm_freq | float | 设定的频率 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveASynmove

**描述：** 运动中触发其他轴的运动. 20170503以上固件支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| base_axis | uint32 | 运动轴轴号 |
| iaxis | uint32 | 触发的轴号 |
| fdist | float | 触发运动的距离 |
| ifsp | uint32 | 触发运动是否为 SP 运动 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveTable

**描述：** 运动中修改TABLE。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| base_axis | uint32 | 插补主轴编号 |
| table_num | uint32 | TABLE编号 |
| fvalue | float | 修改值 |

**返回值：** 错误码。 type: int32

---

### ZAux_Direct_MoveWait

**描述：** BASE轴运动缓冲加入一个可变的延时  固件150802以上版本, 或XPLC160405以上版本支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| base_axis | uint32 | 插补主轴编号 |
| paraname | string | 参数名字符串 DPOS MPOS IN AIN VPSPEED MSPEED MODBUS_REG MODBUS_IEEE MODBUS_BIT NVRAM VECT_BUFFED  REMAIN |
| inum | int | 参数编号或轴号 |
| Cmp_mode | int | 比较条件 1 >=   0=  -1<=  对IN等BIT类型参数无效 |
| fvalue | float | 比较值 |

**返回值：** 错误码。             type: int32

---

### ZAux_Direct_MoveTask

**描述：** BASE轴运动缓冲加入一个TASK任务 当任务已经启动时，会报错，但不影响程序执行。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| base_axis | uint32 | 插补主轴编号 |
| tasknum | uint32 | 任务编号 |
| labelname | sting | BAS中全局函数名或者标号 |

**返回值：** 错误码。             type: int32

---

### ZAux_Direct_Pswitch

**描述：** 位置比较PSWITCH。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| num | int | 比较器编号  0-15 |
| enable | int | 比较器使能 0/1 |
| axisnum | int | 比较的轴号 |
| outnum | int | 输出口编号 |
| outstate | int | 输出状态 0/1 |
| setpos | float | 比较起始位置 |
| resetpos | float | 输出复位位置 |

**返回值：** 错误码。             type: int32

---

### ZAux_Direct_HwPswitch

**描述：** 硬件位置比较输出 4系列产品脉冲轴与编码器轴支持硬件比较输出。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| Axisnum | int | 比较输出的轴号 |
| Mode | int | 比较器操作 1-启动比较器 2-停止并删除未完成的点 |
| Direction | int | 比较方向 0-负向 1-正向 |
| Reserve | int | 预留 |
| Tablestart | int | TABLE 起始地址 |
| Tableend | int | TABLE 结束地址 |

**返回值：** 错误码。             type: int32

---

### ZAux_Direct_GetHwPswitchBuff

**描述：** 硬件位置比较输出剩余缓冲获取 4系列产品脉冲轴与编码器轴支持硬件比较输出。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| axisnum | int | 比较输出的轴号 |

**返回值：** 错误码,位置比较输出剩余缓冲数。             type: int32,int

---

### ZAux_Direct_HwTimer

**描述：** 硬件定时器用于硬件比较输出后一段时间后还原电平 4系列产品支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| mode | int | 模式 |
| cyclonetime | int | 周期时间 us单位 |
| optime | int | 有效时间 us单位 |
| reptimes | int | 重复次数 |
| opstate | int | 输出缺省状态   输出口变为非此状态后开始计时 |
| opnum | int | 输出口编号 必须能硬件比较输出的口 |

**返回值：** 错误码。             type: int32

---

### ZAux_Direct_GetAxisStopReason

**描述：** 读取轴停止原因。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回状态值，对应的位表示不同状态。             type: int32,int

---

### ZAux_Direct_GetAllAxisPara

**描述：** 浮点型读全部轴参数状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| sParam | sting | Baisc 语法参数的字符串名称 |
| imaxaxis | int | 轴数量 |

**返回值：** 错误码,返回状态值。             type: int32,float

---

### ZAux_Direct_GetAllAxisInfo

---

### ZAux_BusCmd_SDOReadAxis

**描述：** 通过轴号进行 SDO 读取。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |
| index | uint32 | 对象字典编号 |
| subindex | uint32 | 对象字典子编号 |
| aype | uint32 | 数据类型  1-bool 2-int8 3-int16 4-int32 5-uint8 6-uint16 7-uint32 |

**返回值：** 错误码, 读取的数据值。 int32,int32

---

### ZAux_BusCmd_SDOWriteAxis

**描述：** 通过轴号进行 SDO 写入。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |
| index | uint32 | 对象字典编号 |
| subindex | uint32 | 对象字典子编号 |
| aype | uint32 | 数据类型  1-bool 2-int8 3-int16 4-int32 5-uint8 6-uint16 7-uint32 |
| Vvalue | uint32 | 对象字典子编号 |

**返回值：** 错误码。    type:int32

---

### ZAux_BusCmd_RtexRead

**描述：** Rtex读取参数信息。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |
| ipara | uint32 | 参数分类*256 + 参数编号  Pr7.11-ipara = 7*256+11 |

**返回值：** 错误码,读取的数据值。    type:int32,float

---

### ZAux_BusCmd_RtexWrite

**描述：** Rtex写参数信息。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |
| ipara | uint32 | 参数分类*256 + 参数编号  Pr7.11-ipara = 7*256+11 |
| vvalue | float | 设定的数据值 |

**返回值：** 错误码。    type:int32

---

### ZAux_BusCmd_SetDatumOffpos

**描述：** 设置回零偏移距离。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |
| fValue | float | 偏移距离 |

**返回值：** 错误码。    type:int32

---

### ZAux_BusCmd_GetDatumOffpos

**描述：** 读取回零偏移距离。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |

**返回值：** 错误码,偏移距离。    type:int32,float

---

### ZAux_BusCmd_Datum

**描述：** 总线驱动器回零。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |
| homemode | uint32 | 回零模式，查看驱动器手册 |

**返回值：** 错误码。    type:int32

---

### ZAux_BusCmd_GetHomeStatus

**描述：** 驱动器回零完成状态。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |

**返回值：** 错误码,零完成标志 0-回零异常 1回零成功。    type:int32,uint32

---

### ZAux_BusCmd_DriveClear

**描述：** 设置清除总线伺服报警。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | uint32 | 轴号 |
| mode | uint32 | 模式 0-清除当前告警  1-清除历史告警  2-清除外部输入告警 |

**返回值：** 错误码。    type:int32

---

### ZAux_BusCmd_GetDriveTorque

**描述：** 读取当前总线驱动当前力矩       需要设置对应的DRIVE_PROFILE类型。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,当前转矩。    type:int32,int

---

### ZAux_BusCmd_SetMaxDriveTorque

**描述：** 设置当前总线驱动最大转矩  需要设置对应的DRIVE_PROFILE类型。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| piValue | int | 最大转矩限制 |

**返回值：** 错误码。    type:int32

---

### ZAux_BusCmd_GetMaxDriveTorque

**描述：** 读取当前总线驱动最大转矩  需要设置对应的DRIVE_PROFILE类型。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |

**返回值：** 错误码,返回的最大转矩。    type:int32,int

---

### ZAux_BusCmd_GetInitStatus

**描述：** 获取总线初始化完成状态(针对Zmotion tools 工具软件配置过总线参数控制器使用有效）。

**返回值：** 错误码,0-初始化失败 1成功。    type:int32,int

---

### ZAux_SetTimeOut

**描述：** 命令的延时等待时间。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| timems | int | 等待时间 MS |

**返回值：** 错误码。    type:int32

---

### ZAux_Direct_HwPswitch2

**描述：** 硬件位置比较输出2 4系列产品, 20170513以上版本支持.  ZMC306E/306N支持。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| Axisnum | int | 比较输出的轴号 |
| Mode | int | 模式 1-启动比较器 |
| Opnum | int | 输出口编号。4 系列 out 0-为硬件位置比较输出 |
| Opstate | int | 第一个比较点的输出状态。 0-关闭 1-打开 |
| ModePara1 | float | 多功能参数 |
| ModePara2 | float | 多功能参数 |
| ModePara3 | float | 多功能参数 |
| ModePara4 | float | 多功能参数 |

**返回值：** 错误码。    type:int32

---

### ZAux_Direct_MultiMovePt

**描述：** 多条相对PT运动 。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iMoveLen | int | 填写的运动数量 |
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| piAxislist | int | 周期列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。    type:int32

---

### ZAux_Direct_MultiMovePtAbs

**描述：** 多条绝对PT运动 。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iMoveLen | int | 填写的运动数量 |
| imaxaxises | int | 参与运动总轴数 |
| piAxislist | int | 轴号列表 |
| piAxislist | int | 周期列表 |
| pfDisancelist | float | 距离列表 |

**返回值：** 错误码。    type:int32

---

### ZAux_Direct_Pitchset

**描述：** 设置轴的螺距补偿，扩展轴无效。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iEnable | int | 是否启用补偿 |
| StartPos | float | 起始补偿MPOS位置 |
| maxpoint | uint32 | 补偿区间总点数 |
| DisOne | float | 每个补偿点间距 |
| TablNum | uint32 | 补偿坐标值填入TABLE系统数组起始引导地址 |
| pfDisancelist | float | 区间补偿值列表 |

**返回值：** 错误码。    type:int32

---

### ZAux_Direct_Pitchset2

**描述：** 设置轴的螺距双向补偿，扩展轴无效。

**输入参数：**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| iaxis | int | 轴号 |
| iEnable | int | 是否启用补偿 |
| StartPos | float | 起始补偿MPOS位置 |
| maxpoint | uint32 | 补偿区间总点数 |
| DisOne | float | 每个补偿点间距 |
| TablNum | uint32 | 补偿坐标值填入TABLE系统数组起始引导地址 |
| pfDisancelist | float | 区间补偿值列表 |
| RevTablNum | uint32 | 反向补偿坐标值填入TABLE系统数组起始引导地址 |
| RevpfDisancelist | float | 反向区间补偿值列表 补偿数据方向于正向方向一致 |

**返回值：** 错误码。    type:int32

---

