# ZMotion DOF5 项目概述

## 项目目的
这是一个基于ZMotion控制器的单轴运动控制项目，主要用于工业自动化中的精密运动控制。项目包含了ZMotion控制器的Python接口库和一个基于PySide2的图形用户界面。

## 技术栈
- **编程语言**: Python
- **GUI框架**: PySide2 (Qt for Python)
- **控制器接口**: ZMotion控制器DLL封装
- **平台支持**: Windows (主要), macOS, Linux

## 项目结构
- `ref/1-Single Axis Motion/`: 主要项目目录
  - `main.py`: 应用程序入口点
  - `Ui_Weiget.py`: 主要UI逻辑类
  - `mainweiget.ui`: Qt Designer UI文件
  - `zmcdll/`: ZMotion控制器Python接口库
    - `zauxdllPython.py`: 主要的DLL封装类
    - `zauxdll.dll`: Windows DLL文件
    - `__init__.py`: 包初始化文件

## 核心功能
- 单轴运动控制
- 实时位置监控
- 速度和加速度控制
- 多种运动模式支持
- 网络和串口通信支持