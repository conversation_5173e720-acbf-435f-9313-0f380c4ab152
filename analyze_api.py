#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API分析脚本 - 用于分析zauxdllPython.py文件并生成API参考文档
"""

import re
import ast
import inspect
from typing import List, Dict, Tuple, Optional

def parse_docstring(docstring: str) -> Dict[str, str]:
    """解析函数的文档字符串"""
    if not docstring:
        return {}
    
    result = {
        'description': '',
        'params': [],
        'returns': ''
    }
    
    lines = docstring.strip().split('\n')
    current_section = None
    
    for line in lines:
        line = line.strip()
        if not line or line in ["'''", '"""']:
            continue
            
        if line.startswith(':Description:'):
            result['description'] = line.replace(':Description:', '').strip()
            current_section = 'description'
        elif line.startswith(':param '):
            # 解析参数: :param param_name:描述。 type: param_type
            param_match = re.match(r':param\s+([^:]+):\s*(.+)', line)
            if param_match:
                param_name = param_match.group(1).strip()
                param_desc = param_match.group(2).strip()
                
                # 提取类型信息
                type_match = re.search(r'type:\s*(\w+)', param_desc)
                param_type = type_match.group(1) if type_match else 'unknown'
                
                # 清理描述（移除type信息）
                param_desc = re.sub(r'\s*type:\s*\w+', '', param_desc).strip()
                param_desc = param_desc.rstrip('。').strip()
                
                result['params'].append({
                    'name': param_name,
                    'type': param_type,
                    'description': param_desc
                })
        elif line.startswith(':Return:'):
            result['returns'] = line.replace(':Return:', '').strip()
    
    return result

def analyze_zauxdll_class(file_path: str) -> List[Dict]:
    """分析ZAUXDLL类中的所有方法"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用AST解析Python代码
    tree = ast.parse(content)
    
    methods = []
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef) and node.name == 'ZAUXDLL':
            for item in node.body:
                if isinstance(item, ast.FunctionDef) and not item.name.startswith('_'):
                    # 获取函数的文档字符串
                    docstring = ast.get_docstring(item)
                    
                    # 解析文档字符串
                    doc_info = parse_docstring(docstring)
                    
                    # 获取函数参数
                    args = []
                    for arg in item.args.args:
                        if arg.arg != 'self':  # 跳过self参数
                            args.append(arg.arg)
                    
                    method_info = {
                        'name': item.name,
                        'args': args,
                        'description': doc_info.get('description', ''),
                        'params': doc_info.get('params', []),
                        'returns': doc_info.get('returns', ''),
                        'line_number': item.lineno
                    }
                    
                    methods.append(method_info)
    
    return sorted(methods, key=lambda x: x['line_number'])

def generate_markdown_api_doc(methods: List[Dict]) -> str:
    """生成Markdown格式的API文档"""
    markdown = """# ZMotion ZAUXDLL Python API 参考文档

本文档包含了ZMotion控制器Python接口库(zauxdllPython.py)中ZAUXDLL类的所有方法。

## 目录

"""
    
    # 生成目录
    categories = {
        '连接管理': ['ZAux_OpenEth', 'ZAux_OpenCom', 'ZAux_OpenPci', 'ZAux_Close', 'ZAux_SearchEth', 'ZAux_SearchEthlist', 'ZAux_SearchAndOpenCom', 'ZAux_FastOpen'],
        '命令执行': ['ZAux_Execute', 'ZAux_DirectCommand'],
        '系统控制': ['ZAux_Resume', 'ZAux_Pause', 'ZAux_BasDown', 'ZAux_ZarDown', 'ZAux_SetRtcTime'],
        'IO控制': ['ZAux_Direct_GetIn', 'ZAux_Direct_SetOp', 'ZAux_Direct_GetOp', 'ZAux_Direct_SetInvertIn', 'ZAux_Direct_GetInvertIn', 'ZAux_Direct_GetInMulti', 'ZAux_Direct_SetOutMulti', 'ZAux_Direct_GetOutMulti'],
        '模拟量控制': ['ZAux_Direct_GetAD', 'ZAux_Direct_SetDA', 'ZAux_Direct_GetDA', 'ZAux_Direct_SetDAC', 'ZAux_Direct_GetDAC'],
        'PWM控制': ['ZAux_Direct_SetPwmFreq', 'ZAux_Direct_SetPwmDuty', 'ZAux_Direct_GetPwmDuty', 'ZAux_Direct_GetPwmFreq'],
        '轴参数设置': ['ZAux_Direct_SetParam', 'ZAux_Direct_GetParam', 'ZAux_Direct_SetAccel', 'ZAux_Direct_GetAccel', 'ZAux_Direct_SetDecel', 'ZAux_Direct_GetDecel', 'ZAux_Direct_SetSpeed', 'ZAux_Direct_GetSpeed'],
        '轴状态读取': ['ZAux_Direct_GetDpos', 'ZAux_Direct_SetDpos', 'ZAux_Direct_GetMpos', 'ZAux_Direct_SetMpos', 'ZAux_Direct_GetMspeed', 'ZAux_Direct_GetAxisStatus', 'ZAux_Direct_GetAxisEnable'],
        '运动控制': ['ZAux_Direct_Move', 'ZAux_Direct_MoveAbs', 'ZAux_Direct_MoveSp', 'ZAux_Direct_MoveAbsSp', 'ZAux_Direct_Single_Move', 'ZAux_Direct_Single_MoveAbs', 'ZAux_Direct_Single_Vmove', 'ZAux_Direct_Single_Cancel'],
        '插补运动': ['ZAux_Direct_MoveCirc', 'ZAux_Direct_MoveCircAbs', 'ZAux_Direct_MoveCircSp', 'ZAux_Direct_MoveCircAbsSp', 'ZAux_Direct_MultiMove', 'ZAux_Direct_MultiMoveAbs'],
        '回零控制': ['ZAux_Direct_Single_Datum', 'ZAux_Direct_UserDatum', 'ZAux_Direct_GetHomeStatus'],
        '总线控制': ['ZAux_BusCmd_GetNodeNum', 'ZAux_BusCmd_GetNodeInfo', 'ZAux_BusCmd_GetNodeStatus', 'ZAux_BusCmd_SDORead', 'ZAux_BusCmd_SDOWrite', 'ZAux_BusCmd_InitBus'],
        '数据读写': ['ZAux_Direct_SetUserVar', 'ZAux_Direct_GetUserVar', 'ZAux_Direct_SetUserArray', 'ZAux_Direct_GetUserArray', 'ZAux_Direct_SetTable', 'ZAux_Direct_GetTable'],
        '其他功能': []
    }
    
    # 为每个分类生成目录项
    for category, func_names in categories.items():
        if category != '其他功能':
            markdown += f"- [{category}](#{category.replace(' ', '-').lower()})\n"
    
    markdown += "\n---\n\n"
    
    # 按分类生成文档
    for category, func_names in categories.items():
        if category == '其他功能':
            # 收集未分类的函数
            categorized_funcs = set()
            for cat_funcs in categories.values():
                categorized_funcs.update(cat_funcs)
            
            uncategorized = [m for m in methods if m['name'] not in categorized_funcs]
            if uncategorized:
                markdown += f"## {category}\n\n"
                for method in uncategorized:
                    markdown += generate_method_doc(method)
        else:
            # 生成已分类的函数文档
            category_methods = [m for m in methods if m['name'] in func_names]
            if category_methods:
                markdown += f"## {category}\n\n"
                for method in category_methods:
                    markdown += generate_method_doc(method)
    
    return markdown

def generate_method_doc(method: Dict) -> str:
    """生成单个方法的文档"""
    doc = f"### {method['name']}\n\n"
    
    if method['description']:
        doc += f"**描述：** {method['description']}\n\n"
    
    # 输入参数
    if method['params']:
        doc += "**输入参数：**\n\n"
        doc += "| 参数名 | 类型 | 描述 |\n"
        doc += "|--------|------|------|\n"
        for param in method['params']:
            doc += f"| {param['name']} | {param['type']} | {param['description']} |\n"
        doc += "\n"
    
    # 返回值
    if method['returns']:
        doc += f"**返回值：** {method['returns']}\n\n"
    
    doc += "---\n\n"
    
    return doc

def main():
    """主函数"""
    file_path = "ref/1-Single Axis Motion/zmcdll/zauxdllPython.py"
    
    print("正在分析zauxdllPython.py文件...")
    methods = analyze_zauxdll_class(file_path)
    
    print(f"找到 {len(methods)} 个方法")
    
    print("正在生成API文档...")
    markdown_doc = generate_markdown_api_doc(methods)
    
    # 写入文档
    output_path = "api_ref.md"
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(markdown_doc)
    
    print(f"API文档已生成: {output_path}")

if __name__ == "__main__":
    main()