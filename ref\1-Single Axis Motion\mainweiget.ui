<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>408</width>
    <height>565</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QGroupBox" name="groupBox_4">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>290</y>
     <width>151</width>
     <height>261</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="title">
    <string>Parameters Configuration</string>
   </property>
   <widget class="QLabel" name="label_5">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>22</y>
      <width>48</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>UNITS</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_6">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>64</y>
      <width>48</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>Start Speed</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_Units">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>22</y>
      <width>71</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_Lspeed">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>64</y>
      <width>71</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_Speed">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>106</y>
      <width>71</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_Accel">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>147</y>
      <width>71</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_Decel">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>189</y>
      <width>71</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_Sramp">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>231</y>
      <width>71</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QLabel" name="label_7">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>106</y>
      <width>24</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>SPEED</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_8">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>147</y>
      <width>36</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>ACCEL</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_9">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>189</y>
      <width>36</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>DECEL</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_10">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>231</y>
      <width>54</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>S Curve</string>
    </property>
   </widget>
  </widget>
  <widget class="QGroupBox" name="groupBox_3">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>170</y>
     <width>201</width>
     <height>111</height>
    </rect>
   </property>
   <property name="title">
    <string>Axis Selection</string>
   </property>
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QRadioButton" name="radio_X">
      <property name="text">
       <string>X</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QRadioButton" name="radio_Y">
      <property name="text">
       <string>Y</string>
      </property>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QRadioButton" name="radio_Z">
      <property name="text">
       <string>Z</string>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QRadioButton" name="radio_R">
      <property name="text">
       <string>R</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QGroupBox" name="groupBox_2">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>170</y>
     <width>181</width>
     <height>111</height>
    </rect>
   </property>
   <property name="title">
    <string>Axis Status</string>
   </property>
   <layout class="QFormLayout" name="formLayout">
    <property name="topMargin">
     <number>9</number>
    </property>
    <property name="rightMargin">
     <number>9</number>
    </property>
    <property name="bottomMargin">
     <number>9</number>
    </property>
    <item row="0" column="0">
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>Now State</string>
      </property>
      <property name="margin">
       <number>5</number>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QLineEdit" name="edit_State">
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QLabel" name="label_3">
      <property name="text">
       <string>Now Position</string>
      </property>
      <property name="margin">
       <number>5</number>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QLineEdit" name="edit_Dpos">
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item row="2" column="0">
     <widget class="QLabel" name="label_4">
      <property name="text">
       <string>Now Speed</string>
      </property>
      <property name="margin">
       <number>5</number>
      </property>
      <property name="indent">
       <number>-1</number>
      </property>
      <property name="openExternalLinks">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item row="2" column="1">
     <widget class="QLineEdit" name="edit_Vspeed">
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QGroupBox" name="groupBox">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>351</width>
     <height>145</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="title">
    <string>CONNECT</string>
   </property>
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>22</y>
      <width>16</width>
      <height>16</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="text">
     <string>IP</string>
    </property>
   </widget>
   <widget class="QComboBox" name="comboBox">
    <property name="geometry">
     <rect>
      <x>46</x>
      <y>23</y>
      <width>131</width>
      <height>20</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="editable">
     <bool>true</bool>
    </property>
    <property name="currentText">
     <string/>
    </property>
   </widget>
   <widget class="QPushButton" name="btn_close">
    <property name="geometry">
     <rect>
      <x>266</x>
      <y>80</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="text">
     <string>DISCONNECT</string>
    </property>
   </widget>
   <widget class="QPushButton" name="btn_open">
    <property name="geometry">
     <rect>
      <x>266</x>
      <y>22</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="text">
     <string>Connect</string>
    </property>
   </widget>
   <widget class="QPushButton" name="btn_ip_scan">
    <property name="geometry">
     <rect>
      <x>185</x>
      <y>22</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>IP Scan</string>
    </property>
   </widget>
   <widget class="QPushButton" name="btn_com">
    <property name="geometry">
     <rect>
      <x>185</x>
      <y>52</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>Link</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_12">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>51</y>
      <width>24</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>Serial</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_13">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>83</y>
      <width>18</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>PCI</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_com">
    <property name="geometry">
     <rect>
      <x>46</x>
      <y>51</y>
      <width>133</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_pci">
    <property name="geometry">
     <rect>
      <x>46</x>
      <y>84</y>
      <width>133</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QPushButton" name="btn_pci">
    <property name="geometry">
     <rect>
      <x>185</x>
      <y>83</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>Link</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_14">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>112</y>
      <width>30</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>LOCAL</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="edit_local">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>46</x>
      <y>113</y>
      <width>133</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QPushButton" name="btn_local">
    <property name="geometry">
     <rect>
      <x>185</x>
      <y>112</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>Link</string>
    </property>
   </widget>
  </widget>
  <widget class="QGroupBox" name="groupBox_9">
   <property name="geometry">
    <rect>
     <x>180</x>
     <y>490</y>
     <width>221</width>
     <height>61</height>
    </rect>
   </property>
   <property name="title">
    <string/>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_3">
    <item>
     <widget class="QPushButton" name="btn_Run">
      <property name="text">
       <string>RUN</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="btn_Stop">
      <property name="text">
       <string>STOP</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="btn_Clear">
      <property name="text">
       <string>CLEAR</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QGroupBox" name="groupBox_5">
   <property name="geometry">
    <rect>
     <x>180</x>
     <y>290</y>
     <width>221</width>
     <height>192</height>
    </rect>
   </property>
   <property name="title">
    <string>Mode Selection</string>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QGroupBox" name="groupBox_6">
      <property name="title">
       <string>Direction Selection</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QRadioButton" name="radio_Zheng">
         <property name="text">
          <string>Positive</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="radio_fu">
         <property name="text">
          <string>Negative</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="groupBox_7">
      <property name="title">
       <string>Motion Method</string>
      </property>
      <layout class="QFormLayout" name="formLayout_4">
       <item row="0" column="0">
        <widget class="QRadioButton" name="radio_Vmove">
         <property name="text">
          <string>Continuous</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QRadioButton" name="radio_MoveAbs">
         <property name="text">
          <string>Relative</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="groupBox_8">
      <property name="title">
       <string/>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_11">
         <property name="text">
          <string>Motion Distance</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="edit_Distance"/>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
