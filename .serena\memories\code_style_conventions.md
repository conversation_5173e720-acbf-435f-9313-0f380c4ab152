# 代码风格和约定

## 编码规范
- **字符编码**: UTF-8
- **文件头**: 包含作者信息和文件描述
- **注释语言**: 中文和英文混合

## 命名约定
- **类名**: PascalCase (如 `ZAUXDLL`, `UiInterFace`)
- **方法名**: 保持原DLL函数命名 (如 `ZAux_Execute`, `ZAux_OpenEth`)
- **变量名**: camelCase 和 snake_case 混合使用

## 文档字符串格式
```python
'''
:Description: 函数功能描述
:param param_name: 参数描述。 type: 参数类型
:Return: 返回值描述。 type: 返回类型
'''
```

## 类型提示
- 使用ctypes类型进行C库接口定义
- 参数类型在文档字符串中说明

## 错误处理
- 函数返回错误码和结果的元组
- 使用ctypes进行内存管理